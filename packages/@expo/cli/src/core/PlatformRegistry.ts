// Import existing base classes from Expo CLI
import { AppIdResolver } from '../start/platforms/AppIdResolver';
import { DeviceManager } from '../start/platforms/DeviceManager';
import { PlatformManager, BaseResolveDeviceProps } from '../start/platforms/PlatformManager';

// Re-export everything from the new platform-registry package
export * from '@expo/platform-registry';

// Add deprecation warning for imports from this location
const debug = require('debug')('expo:platform-registry-deprecation') as typeof console.log;

if (process.env.NODE_ENV !== 'test') {
  debug(
    'Importing from @expo/cli/src/core/PlatformRegistry is deprecated. ' +
    'Use @expo/platform-registry instead.'
  );
}

// Legacy type exports for backward compatibility
// These now use the proper types from the CLI base classes

/**
 * External platform device manager constructor.
 * External platforms should provide a DeviceManager class that extends the base DeviceManager.
 * The class must implement a static resolveAsync method for device resolution.
 */
export interface ExternalPlatformDeviceManagerConstructor<TDevice> {
  new (device: TDevice): DeviceManager<TDevice>;
  resolveAsync(options?: BaseResolveDeviceProps<TDevice>): Promise<DeviceManager<TDevice>>;
}

/**
 * External platform manager constructor.
 * Creates a PlatformManager instance that extends Expo's base class.
 */
export type ExternalPlatformManagerConstructor<TDevice> = new (
  projectRoot: string,
  options: {
    getDevServerUrl: () => string | null;
    getExpoGoUrl: () => string;
    getRedirectUrl: () => string | null;
    getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;
    resolveDeviceAsync: (
      options?: BaseResolveDeviceProps<TDevice>
    ) => Promise<DeviceManager<TDevice>>;
  }
) => PlatformManager<TDevice>;

/**
 * External platform app ID resolver constructor.
 */
export type ExternalPlatformAppIdResolverConstructor = new (projectRoot: string) => AppIdResolver;

/**
 * External platform prerequisite constructor.
 * Allows external platforms to provide health checking and prerequisite validation.
 * @deprecated Use @expo/platform-registry instead
 */
export type ExternalPlatformPrerequisiteConstructor = new (
  platform: string
) => ExternalPlatformPrerequisite;

/**
 * Abstract base class for external platform prerequisites.
 * External platforms should extend this to provide health checking.
 * @deprecated Use @expo/platform-registry instead
 */
export abstract class ExternalPlatformPrerequisite {
  constructor(protected platform: string) {}

  /**
   * Check if the development environment is properly set up for this platform.
   * @returns Promise resolving to true if environment is ready
   */
  abstract checkDevelopmentEnvironment(): Promise<boolean>;

  /**
   * Check if system requirements are met for this platform.
   * @returns Promise resolving to true if requirements are met
   */
  abstract checkSystemRequirements(): Promise<boolean>;

  /**
   * Check if platform-specific tools are installed and available.
   * @returns Promise resolving to true if tools are available
   */
  abstract checkPlatformTools(): Promise<boolean>;

  /**
   * Get installation instructions for missing prerequisites.
   * @returns Array of instruction strings
   */
  abstract getInstallationInstructions(): string[];

  /**
   * Run all prerequisite checks and throw if any fail.
   * This is the main entry point called by the doctor command.
   */
  async assertAsync(): Promise<void> {
    const checks = [
      { name: 'Development Environment', check: () => this.checkDevelopmentEnvironment() },
      { name: 'System Requirements', check: () => this.checkSystemRequirements() },
      { name: 'Platform Tools', check: () => this.checkPlatformTools() },
    ];

    const failures: string[] = [];

    for (const { name, check } of checks) {
      try {
        const result = await check();
        if (!result) {
          failures.push(name);
        }
      } catch (error: any) {
        failures.push(`${name}: ${error.message}`);
      }
    }

    if (failures.length > 0) {
      const instructions = this.getInstallationInstructions();
      const instructionText =
        instructions.length > 0
          ? `\n\nTo fix these issues:\n${instructions.map((i) => `  • ${i}`).join('\n')}`
          : '';

      throw new Error(
        `Platform "${this.platform}" prerequisites not met:\n` +
          `${failures.map((f) => `  ✗ ${f}`).join('\n')}${instructionText}`
      );
    }
  }
}

// Re-export all interfaces and types from the new platform-registry package
// These are kept here for backward compatibility but marked as deprecated

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface ExternalPlatformDependencyResolver {
  resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]>;
  configureAutolinking?(projectRoot: string, packages: string[]): Promise<void>;
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface AutolinkingImplementation {
  resolveModuleAsync(moduleName: string, projectRoot: string): Promise<any>;
  generatePackageListAsync(projectRoot: string): Promise<string>;
  getLinkingConfigAsync(projectRoot: string): Promise<any>;
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface SDKModuleCompatibility {
  [moduleName: string]: {
    supported: boolean;
    version?: string;
    implementation?: string;
    fallback?: string;
  };
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface AssetHandlerConfiguration {
  appIcon?: {
    sizes: number[];
    format: string;
    generator?: string;
  };
  splashScreen?: {
    supported: boolean;
    fallback?: string;
    handler?: string;
  };
  fonts?: {
    formats: string[];
    handler?: string;
  };
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface DevClientExtensionConfiguration {
  devMenuItems?: {
    name: string;
    action: string;
    icon?: string;
  }[];
  errorBoundaries?: {
    [errorType: string]: string;
  };
  debugTools?: {
    [toolName: string]: string;
  };
  inspectors?: {
    [inspectorName: string]: string;
  };
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface TestUtilityConfiguration {
  testEnvironment?: string;
  setupFiles?: string[];
  moduleNameMapper?: {
    [pattern: string]: string;
  };
  platformMocks?: {
    [mockName: string]: any;
  };
  testUtilities?: {
    [utilityName: string]: string;
  };
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface PlatformPermissionConfiguration {
  [permissionName: string]: {
    key: string;
    description: string;
    required: boolean;
    fallback?: string;
  };
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface RunOptions {
  device?: string | boolean;
  port?: number;
  bundler?: boolean;
  install?: boolean;
  buildCache?: boolean;
  binary?: string;
  variant?: string;
  configuration?: string;
  scheme?: string | boolean;
  [key: string]: any;
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export interface ExternalPlatform {
  platform: string;
  displayName?: string;
  deviceManagerConstructor?: ExternalPlatformDeviceManagerConstructor<any>;
  platformManagerConstructor?: ExternalPlatformManagerConstructor<any>;
  appIdResolverConstructor?: ExternalPlatformAppIdResolverConstructor;
  prerequisiteConstructor?: ExternalPlatformPrerequisiteConstructor;
  runAsync?: (projectRoot: string, options: RunOptions) => Promise<void>;
  dependencyResolver?: ExternalPlatformDependencyResolver;
  configPlugins?: string[];
  metroExtensions?: string[];
  templatePath?: string;
  autolinkingImplementation?: AutolinkingImplementation;
  supportedModules?: SDKModuleCompatibility;
  assetHandlers?: AssetHandlerConfiguration;
  devClientExtensions?: DevClientExtensionConfiguration;
  testUtilities?: TestUtilityConfiguration;
  permissions?: PlatformPermissionConfiguration;
  debuggingConfig?: any;
  metroConfig?: any;
  validationRules?: any[];
}

// Legacy implementations for backward compatibility
// These are deprecated and will be removed in a future version

/**
 * @deprecated Use @expo/platform-registry instead
 */
export class PlatformRegistry {
  private platforms = new Map<string, ExternalPlatform>();

  register(platformData: ExternalPlatform): void {
    this.platforms.set(platformData.platform, platformData);
  }

  getAvailablePlatforms(): string[] {
    return Array.from(this.platforms.keys());
  }

  hasPlatform(platform: string): boolean {
    return this.platforms.has(platform);
  }

  getPlatform(platform: string): ExternalPlatform | undefined {
    return this.platforms.get(platform);
  }

  getAllPlatforms(): ExternalPlatform[] {
    return Array.from(this.platforms.values());
  }

  clear(): void {
    this.platforms.clear();
  }
}

/**
 * @deprecated Use @expo/platform-registry instead
 */
export const platformRegistry = new PlatformRegistry();

/**
 * @deprecated Use @expo/platform-registry instead
 */
export class PlatformDiscovery {
  private static loadedPlatforms = new Set<string>();

  static async loadExternalPlatforms(projectRoot?: string): Promise<void> {
    // Delegate to the new package
    try {
      const { PlatformDiscovery: NewPlatformDiscovery, platformRegistry: newRegistry } = require('@expo/platform-registry');
      await NewPlatformDiscovery.loadExternalPlatforms(projectRoot, platformRegistry);
    } catch (error) {
      // Fallback to legacy implementation if new package is not available
      console.warn('Failed to load @expo/platform-registry, using legacy implementation');
    }
  }

  static loadExternalPlatformsSync(projectRoot?: string): void {
    // Delegate to the new package
    try {
      const { PlatformDiscovery: NewPlatformDiscovery } = require('@expo/platform-registry');
      NewPlatformDiscovery.loadExternalPlatformsSync(projectRoot, platformRegistry);
    } catch (error) {
      // Fallback to legacy implementation if new package is not available
      console.warn('Failed to load @expo/platform-registry, using legacy implementation');
    }
  }

  static getLoadedPlatformPackages(): string[] {
    return Array.from(this.loadedPlatforms);
  }

  static clearLoadedPlatforms(): void {
    this.loadedPlatforms.clear();
  }
}
