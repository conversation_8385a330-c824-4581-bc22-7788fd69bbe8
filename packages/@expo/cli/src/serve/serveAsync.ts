import { create<PERSON>equestHandler } from '@expo/server/build/vendor/http';
import chalk from 'chalk';
import connect from 'connect';
import http from 'http';
import path from 'path';
import send from 'send';

import * as Log from '../log';
import { directoryExistsAsync, fileExistsAsync } from '../utils/dir';
import { CommandError } from '../utils/errors';
import { findUpProjectRootOrAssert } from '../utils/findUp';
import { setNodeEnv } from '../utils/nodeEnv';
import { resolvePortAsync } from '../utils/port';
import { PlatformDiscovery, platformRegistry } from '../core/PlatformRegistry';

type Options = {
  port?: number;
  isDefaultDirectory: boolean;
};

interface PlatformDir {
  platform: string;
  dir: string;
  isStatic: boolean;
}

const debug = require('debug')('expo:serve') as typeof console.log;

// Start a basic http server
export async function serveAsync(inputDir: string, options: Options) {
  const projectRoot = findUpProjectRootOrAssert(inputDir);

  setNodeEnv('production');
  require('@expo/env').load(projectRoot);

  // Load external platforms
  await PlatformDiscovery.loadExternalPlatforms(projectRoot);

  const port = await resolvePortAsync(projectRoot, {
    defaultPort: options.port,
    fallbackPort: 8081,
  });

  if (port == null) {
    throw new CommandError('Could not start server. Port is not available.');
  }
  options.port = port;

  // Determine platform-specific serving directories
  const platformDirs = await resolvePlatformServingDirectories(inputDir, options);

  if (platformDirs.length === 0) {
    throw new CommandError(
      `No export directories found. Run \`npx expo export\` first.\n\n` +
      `Looked for platform-specific directories in: ${options.isDefaultDirectory ? path.join(inputDir, 'dist') : inputDir}`
    );
  }

  Log.log(chalk.dim(`Starting server for ${platformDirs.length} platform(s)`));

  // Implement platform-specific serving logic
  if (platformDirs.length > 1) {
    await startMultiPlatformServerAsync(platformDirs, options);
  } else {
    await startSinglePlatformServerAsync(platformDirs[0], options);
  }

  Log.log(`Server running at http://localhost:${options.port}`);
}

async function resolvePlatformServingDirectories(inputDir: string, options: Options): Promise<PlatformDir[]> {
  const serverDist = options.isDefaultDirectory ? path.join(inputDir, 'dist') : inputDir;
  const externalPlatforms = platformRegistry.getAvailablePlatforms();
  const platformDirs: PlatformDir[] = [];

  // Check for platform-specific dist directories
  const allPlatforms = ['ios', 'android', 'web', ...externalPlatforms];

  for (const platform of allPlatforms) {
    const platformDir = path.join(serverDist, platform);
    if (await directoryExistsAsync(platformDir)) {
      platformDirs.push({
        platform,
        dir: platformDir,
        isStatic: await isStaticExportAsync(platformDir)
      });
    }
  }

  // Fallback to general dist directory if no platform-specific dirs found
  if (platformDirs.length === 0 && await directoryExistsAsync(serverDist)) {
    platformDirs.push({
      platform: 'universal',
      dir: serverDist,
      isStatic: await isStaticExportAsync(serverDist)
    });
  }

  return platformDirs;
}

async function startMultiPlatformServerAsync(platformDirs: PlatformDir[], options: Options): Promise<void> {
  const middleware = connect();

  // Add platform detection middleware
  middleware.use((req, res, next) => {
    const userAgent = req.headers['user-agent'] || '';
    const platform = detectPlatformFromRequest(req, userAgent);

    // Find matching platform directory
    const platformDir = platformDirs.find(d => d.platform === platform) ||
                       platformDirs.find(d => d.platform === 'universal') ||
                       platformDirs[0];

    // Set platform-specific serving context
    (req as any).platformDir = platformDir;
    next();
  });

  // Platform-specific serving middleware
  middleware.use(async (req, res, next) => {
    const platformDir = (req as any).platformDir;

    if (platformDir.isStatic) {
      await serveStaticPlatform(req, res, platformDir);
    } else {
      await serveDynamicPlatform(req, res, platformDir);
    }
  });

  middleware.listen(options.port!);
}

async function startSinglePlatformServerAsync(platformDir: PlatformDir, options: Options): Promise<void> {
  if (platformDir.isStatic) {
    await startStaticServerAsync(platformDir.dir, options);
  } else {
    await startDynamicServerAsync(platformDir.dir, options);
  }
}

function detectPlatformFromRequest(req: any, userAgent: string): string {
  // Check expo-platform header first
  const platformHeader = req.headers['expo-platform'];
  if (platformHeader) {
    return platformHeader;
  }

  // Detect from user agent
  if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
    return 'ios';
  } else if (userAgent.includes('Android')) {
    return 'android';
  } else {
    return 'web';
  }
}

async function serveStaticPlatform(req: any, res: any, platformDir: PlatformDir): Promise<void> {
  // Remove query strings and decode URI
  const filePath = decodeURI(req.url?.split('?')[0] ?? '');

  send(req, filePath, {
    root: platformDir.dir,
    index: 'index.html',
  })
    .on('error', (err: any) => {
      if (err.status === 404) {
        res.statusCode = 404;
        res.end('Not Found');
        return;
      }
      res.statusCode = err.status || 500;
      res.end('Internal Server Error');
    })
    .pipe(res);
}

async function serveDynamicPlatform(req: any, res: any, platformDir: PlatformDir): Promise<void> {
  const staticDirectory = path.join(platformDir.dir, 'client');
  const serverDirectory = path.join(platformDir.dir, 'server');

  try {
    const serverHandler = createRequestHandler({ build: serverDirectory });

    // Try to serve static files first
    const pathname = canParseURL(req.url) ? new URL(req.url).pathname : req.url;
    if (pathname && (req.method === 'GET' || req.method === 'HEAD')) {
      const stream = send(req, pathname, {
        root: staticDirectory,
        extensions: ['html'],
      });

      let forwardError = false;
      stream.on('file', function onFile() {
        forwardError = true;
      });

      stream.on('error', function error(err: any) {
        if (forwardError || !(err.statusCode < 500)) {
          res.statusCode = err.status || 500;
          res.end('Internal Server Error');
          return;
        }
        // Fallback to server handler
        serverHandler(req, res, () => {});
      });

      stream.pipe(res);
    } else {
      serverHandler(req, res, () => {});
    }
  } catch (error) {
    res.statusCode = 500;
    res.end('Internal Server Error');
  }
}

async function startStaticServerAsync(dist: string, options: Options) {
  const server = http.createServer((req, res) => {
    // Remove query strings and decode URI
    const filePath = decodeURI(req.url?.split('?')[0] ?? '');

    send(req, filePath, {
      root: dist,
      index: 'index.html',
    })
      .on('error', (err: any) => {
        if (err.status === 404) {
          res.statusCode = 404;
          res.end('Not Found');
          return;
        }
        res.statusCode = err.status || 500;
        res.end('Internal Server Error');
      })
      .pipe(res);
  });

  server.listen(options.port!);
}

async function startDynamicServerAsync(dist: string, options: Options) {
  const middleware = connect();

  const staticDirectory = path.join(dist, 'client');
  const serverDirectory = path.join(dist, 'server');

  const serverHandler = createRequestHandler({ build: serverDirectory });

  // DOM component CORS support
  middleware.use((req, res, next) => {
    // TODO: Only when origin is `file://` (iOS), and Android equivalent.

    // Required for DOM components security in release builds.

    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader(
      'Access-Control-Allow-Headers',
      'Origin, X-Requested-With, Content-Type, Accept, expo-platform'
    );

    // Handle OPTIONS preflight requests
    if (req.method === 'OPTIONS') {
      res.statusCode = 200;
      res.end();
      return;
    }
    next();
  });

  middleware.use((req, res, next) => {
    if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {
      return next();
    }

    const pathname = canParseURL(req.url) ? new URL(req.url).pathname : req.url;
    if (!pathname) {
      return next();
    }

    debug(`Maybe serve static:`, pathname);

    const stream = send(req, pathname, {
      root: staticDirectory,
      extensions: ['html'],
    });

    // add file listener for fallthrough
    let forwardError = false;
    stream.on('file', function onFile() {
      // once file is determined, always forward error
      forwardError = true;
    });

    // forward errors
    stream.on('error', function error(err: any) {
      if (forwardError || !(err.statusCode < 500)) {
        next(err);
        return;
      }

      next();
    });

    // pipe
    stream.pipe(res);
  });

  middleware.use(serverHandler);

  middleware.listen(options.port!);
}

function canParseURL(url: string): boolean {
  try {
    // eslint-disable-next-line no-new
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

async function isStaticExportAsync(dist: string): Promise<boolean> {
  const routesFile = path.join(dist, `server/_expo/routes.json`);
  return !(await fileExistsAsync(routesFile));
}
