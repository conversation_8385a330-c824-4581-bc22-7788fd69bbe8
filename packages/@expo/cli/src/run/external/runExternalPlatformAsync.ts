#!/usr/bin/env node
import chalk from 'chalk';
import { spawn } from 'child_process';
import path from 'path';

import { PlatformDiscovery, platformRegistry, RunOptions } from '@expo/platform-registry';
import * as Log from '../../log';
import { CommandError } from '../../utils/errors';

/**
 * Run an external platform using either the platform's custom runAsync function
 * or fallback to React Native CLI.
 * This provides a proper implementation for external platform run commands.
 */
export async function runExternalPlatformAsync(
  platform: string,
  options: RunOptions = {}
): Promise<void> {
  // Load external platforms to ensure they're available
  await PlatformDiscovery.loadExternalPlatforms();

  const platformData = platformRegistry.getPlatform(platform);

  if (!platformData) {
    throw new CommandError(
      `Platform "${platform}" is not available.\n\n` +
        `To use this platform, install the platform package:\n` +
        `  ${chalk.cyan(`npm install expo-platform-${platform}`)}\n\n` +
        `Or check if you have a typo in the platform name.\n` +
        `Available platforms: ${chalk.gray(platformRegistry.getAvailablePlatforms().join(', ') || 'none')}`
    );
  }

  Log.log(chalk.cyan(`Running ${platform} app...`));

  const projectRoot = process.cwd();

  // Check if the platform provides a custom runAsync function
  if (platformData.runAsync) {
    try {
      Log.log(chalk.gray(`Using platform-specific run implementation for ${platform}`));
      await platformData.runAsync(projectRoot, options);
      return;
    } catch (error: any) {
      Log.error(chalk.red(`Platform-specific run failed: ${error.message}`));
      Log.log(chalk.gray('Falling back to React Native CLI...'));
    }
  }

  // Fallback to React Native CLI implementation
  await runWithReactNativeCLI(platform, projectRoot, options);
}

/**
 * Fallback implementation using React Native CLI.
 * This is used when the platform doesn't provide a custom runAsync function.
 */
async function runWithReactNativeCLI(
  platform: string,
  projectRoot: string,
  options: RunOptions
): Promise<void> {
  // Check if the native project exists (should be generated by expo prebuild)
  const platformDir = path.join(projectRoot, platform);

  try {
    const fs = require('fs');
    if (!fs.existsSync(platformDir)) {
      Log.warn(chalk.yellow(`Native ${platform} project not found.`));
      Log.log(
        chalk.gray(
          `The native ${platform} project needs to be generated before you can run it.\n` +
            `Run the following command to generate the native project:\n\n` +
            `  ${chalk.cyan(`expo prebuild --platform ${platform}`)}\n`
        )
      );
      throw new CommandError(
        `Native ${platform} project not found. Run 'expo prebuild --platform ${platform}' to generate it.`
      );
    }
  } catch (error: any) {
    if (error instanceof CommandError) {
      throw error;
    }
    // If we can't check the directory, continue anyway
    Log.warn(chalk.yellow(`Could not verify native ${platform} project exists.`));
  }

  // Convert RunOptions to React Native CLI arguments
  const args = convertOptionsToArgs(options);

  // Use React Native CLI to run the platform
  const command = `react-native`;
  const commandArgs = [`run-${platform}`, ...args];

  Log.log(chalk.gray(`Executing: npx ${command} ${commandArgs.join(' ')}`));

  return new Promise((resolve, reject) => {
    const child = spawn('npx', [command, ...commandArgs], {
      stdio: 'inherit',
      cwd: projectRoot,
    });

    child.on('error', (error) => {
      if (error.message.includes('ENOENT')) {
        reject(
          new CommandError(
            `React Native CLI is required to run ${platform} apps.\n\n` +
              `Install it globally with:\n` +
              `  ${chalk.cyan('npm install -g @react-native-community/cli')}\n\n` +
              `Or install it locally in your project:\n` +
              `  ${chalk.cyan('npm install @react-native-community/cli')}`
          )
        );
      } else {
        reject(
          new CommandError(
            `Failed to run ${platform} app.\n\n` +
              `Error: ${error.message}\n\n` +
              `This might be due to:\n` +
              `• Missing platform-specific dependencies\n` +
              `• Incorrect platform configuration\n` +
              `• Platform package compatibility issues\n\n` +
              `Check the platform documentation for troubleshooting steps.`
          )
        );
      }
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(
          new CommandError(
            `${platform} app failed to run (exit code: ${code}).\n\n` +
              `Common solutions:\n` +
              `• Check that all platform dependencies are installed\n` +
              `• Verify the platform configuration is correct\n` +
              `• Run ${chalk.cyan(`expo prebuild --platform ${platform} --clean`)} to regenerate the project\n` +
              `• Check the platform package documentation for specific requirements`
          )
        );
      }
    });
  });
}

/**
 * Convert RunOptions to React Native CLI arguments.
 */
function convertOptionsToArgs(options: RunOptions): string[] {
  const args: string[] = [];

  if (options.device) {
    if (typeof options.device === 'string') {
      args.push('--device', options.device);
    } else if (options.device === true) {
      args.push('--device');
    }
  }

  if (options.port) {
    args.push('--port', options.port.toString());
  }

  if (options.variant) {
    args.push('--variant', options.variant);
  }

  if (options.configuration) {
    args.push('--configuration', options.configuration);
  }

  if (options.scheme) {
    if (typeof options.scheme === 'string') {
      args.push('--scheme', options.scheme);
    } else if (options.scheme === true) {
      args.push('--scheme');
    }
  }

  if (options.binary) {
    args.push('--binary', options.binary);
  }

  // Add any additional platform-specific options
  Object.entries(options).forEach(([key, value]) => {
    if (!['device', 'port', 'variant', 'configuration', 'scheme', 'binary', 'bundler', 'install', 'buildCache'].includes(key)) {
      if (typeof value === 'string') {
        args.push(`--${key}`, value);
      } else if (typeof value === 'boolean' && value) {
        args.push(`--${key}`);
      }
    }
  });

  return args;
}
