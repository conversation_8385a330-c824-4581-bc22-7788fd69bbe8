/**
 * Integration tests for external platform system
 * Tests the complete external platform workflow from discovery to execution
 */

import { vol } from 'memfs';

import { PlatformDiscovery, platformRegistry } from '@expo/platform-registry';

// Mock file system
jest.mock('fs');
jest.mock('fs/promises');

describe('External Platform Integration', () => {
  beforeEach(() => {
    // Clear platform registry
    platformRegistry.clear();
    
    // Reset file system
    vol.reset();
    
    // Mock project structure
    vol.fromJSON({
      '/test-project/package.json': JSON.stringify({
        name: 'test-project',
        dependencies: {
          'expo-platform-windows': '1.0.0',
        },
      }),
      '/test-project/app.json': JSON.stringify({
        expo: {
          name: 'Test Project',
          platforms: ['ios', 'android', 'windows'],
        },
      }),
      '/test-project/node_modules/expo-platform-windows/package.json': JSON.stringify({
        name: 'expo-platform-windows',
        version: '1.0.0',
        main: 'build/index.js',
      }),
      '/test-project/node_modules/expo-platform-windows/build/index.js': `
        const { platformRegistry } = require('@expo/platform-registry');
        
        platformRegistry.register({
          platform: 'windows',
          displayName: 'Windows',
          runAsync: async (projectRoot, options) => {
            console.log('Running Windows app');
          },
          metroExtensions: ['.windows.js', '.windows.ts'],
          configPlugins: ['expo-platform-windows/plugin'],
        });
      `,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Platform Discovery', () => {
    it('should discover and load external platforms', async () => {
      await PlatformDiscovery.loadExternalPlatforms('/test-project');
      
      expect(platformRegistry.getAvailablePlatforms()).toContain('windows');
      expect(platformRegistry.hasPlatform('windows')).toBe(true);
      
      const windowsPlatform = platformRegistry.getPlatform('windows');
      expect(windowsPlatform).toBeDefined();
      expect(windowsPlatform?.displayName).toBe('Windows');
      expect(windowsPlatform?.metroExtensions).toEqual(['.windows.js', '.windows.ts']);
    });

    it('should handle missing platform packages gracefully', async () => {
      // Remove the platform package
      vol.unlinkSync('/test-project/node_modules/expo-platform-windows/build/index.js');
      
      await expect(PlatformDiscovery.loadExternalPlatforms('/test-project')).resolves.not.toThrow();
      expect(platformRegistry.getAvailablePlatforms()).not.toContain('windows');
    });
  });

  describe('Run Command Integration', () => {
    it('should delegate to platform runAsync when available', async () => {
      const mockRunAsync = jest.fn().mockResolvedValue(undefined);
      
      platformRegistry.register({
        platform: 'windows',
        displayName: 'Windows',
        runAsync: mockRunAsync,
      });

      const { runExternalPlatformAsync } = await import('../run/external/runExternalPlatformAsync');
      
      await runExternalPlatformAsync('windows', {
        device: 'desktop',
        port: 8081,
      });

      expect(mockRunAsync).toHaveBeenCalledWith('/test-project', {
        device: 'desktop',
        port: 8081,
      });
    });

    it('should fallback to React Native CLI when runAsync not available', async () => {
      platformRegistry.register({
        platform: 'windows',
        displayName: 'Windows',
        // No runAsync function
      });

      const mockSpawn = jest.fn().mockImplementation(() => ({
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(0); // Success
          }
        }),
      }));

      jest.doMock('child_process', () => ({
        spawn: mockSpawn,
      }));

      const { runExternalPlatformAsync } = await import('../run/external/runExternalPlatformAsync');
      
      await runExternalPlatformAsync('windows', {});

      expect(mockSpawn).toHaveBeenCalledWith('npx', ['react-native', 'run-windows'], expect.any(Object));
    });
  });

  describe('Metro Integration', () => {
    it('should add external platform extensions to Metro config', async () => {
      platformRegistry.register({
        platform: 'windows',
        metroExtensions: ['.windows.js', '.windows.ts'],
      });

      const { withExternalPlatforms } = await import('@expo/metro-config/src/withExternalPlatforms');
      
      const config = {
        resolver: {
          platforms: ['ios', 'android'],
          sourceExts: ['js', 'ts'],
        },
      };

      const updatedConfig = withExternalPlatforms(config, platformRegistry);

      expect(updatedConfig.resolver.platforms).toContain('windows');
      expect(updatedConfig.resolver.sourceExts).toContain('.windows.js');
      expect(updatedConfig.resolver.sourceExts).toContain('.windows.ts');
    });
  });

  describe('Config Plugin Integration', () => {
    it('should apply external platform config plugins', async () => {
      const mockConfigPlugin = jest.fn((config) => ({
        ...config,
        windows: { customSetting: true },
      }));

      platformRegistry.register({
        platform: 'windows',
        configPlugins: [mockConfigPlugin],
      });

      const { withExternalPlatformPlugins } = await import('../prebuild/withExternalPlatformPlugins');
      
      const config = { name: 'Test App' };
      const updatedConfig = withExternalPlatformPlugins(config);

      expect(updatedConfig.windows).toEqual({ customSetting: true });
    });
  });

  describe('Error Handling', () => {
    it('should provide helpful error messages for missing platforms', async () => {
      const { runExternalPlatformAsync } = await import('../run/external/runExternalPlatformAsync');
      
      await expect(runExternalPlatformAsync('nonexistent', {})).rejects.toThrow(
        'Platform "nonexistent" is not available'
      );
    });

    it('should handle platform loading errors gracefully', async () => {
      // Mock a platform that throws during loading
      vol.fromJSON({
        '/test-project/node_modules/expo-platform-broken/build/index.js': `
          throw new Error('Platform loading failed');
        `,
      });

      await expect(PlatformDiscovery.loadExternalPlatforms('/test-project')).resolves.not.toThrow();
    });
  });
});
