import { getConfig } from '@expo/config';

import { queryAndGenerateAsync, selectAndGenerateAsync } from './generate';
import { Options } from './resolveOptions';
import { DestinationResolutionProps } from './templates';
import { getRouterDirectoryModuleIdWithManifest } from '../start/server/metro/router';
import { getPlatformBundlers } from '../start/server/platformBundlers';
import { findUpProjectRootOrAssert } from '../utils/findUp';
import { setNodeEnv } from '../utils/nodeEnv';
import { PlatformDiscovery, platformRegistry } from '../core/PlatformRegistry';
import { CommandError } from '../utils/errors';
import { Log } from '../log';

export async function customizeAsync(files: string[], options: Options, extras: any[]) {
  setNodeEnv('development');
  // Locate the project root based on the process current working directory.
  // This enables users to run `npx expo customize` from a subdirectory of the project.
  const projectRoot = findUpProjectRootOrAssert(process.cwd());

  require('@expo/env').load(projectRoot);

  // Load external platforms before processing
  await PlatformDiscovery.loadExternalPlatforms(projectRoot);

  // Get the static path (defaults to 'web/')
  // Doesn't matter if expo is installed or which mode is used.
  const { exp } = getConfig(projectRoot, {
    skipSDKVersionRequirement: true,
  });

  const routerRoot = getRouterDirectoryModuleIdWithManifest(projectRoot, exp);

  // Get platform bundlers to determine web static path
  const platformBundlers = await getPlatformBundlers(projectRoot, exp);

  // Get all available platforms including external ones
  const availablePlatforms = [
    'ios', 'android', 'web',
    ...platformRegistry.getAvailablePlatforms()
  ];

  // Filter platforms based on bundler support and project configuration
  const supportedPlatforms = availablePlatforms.filter(platform => {
    const bundler = platformBundlers[platform];
    return bundler && (exp.platforms?.includes(platform as any) || ['ios', 'android', 'web'].includes(platform));
  });

  if (supportedPlatforms.length === 0) {
    throw new CommandError(
      'No platforms are configured for customization.\n\n' +
      'Make sure your app.json includes platforms that support Metro bundling.'
    );
  }

  // Create the destination resolution props which are used in both
  // the query and select functions.
  const props: DestinationResolutionProps = {
    webStaticPath: (exp.web?.staticPath ?? platformBundlers.web === 'webpack') ? 'web' : 'public',
    appDirPath: routerRoot,
  };

  // If the user provided files, we'll generate them without prompting.
  if (files.length) {
    return queryAndGenerateAsync(projectRoot, {
      files,
      props,
      extras,
    });
  }

  // Otherwise, we'll prompt the user to select which files to generate.
  await selectAndGenerateAsync(projectRoot, {
    props,
    extras,
  });
}
